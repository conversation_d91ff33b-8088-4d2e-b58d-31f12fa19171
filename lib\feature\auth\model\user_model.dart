class UserModel {
  String? name;
  String? email;
  String? image;
  String? uId;
  bool? isVerified;

  UserModel({
    this.name,
    this.email,
    this.image,
    this.uId,
    this.isVerified,
  });
  UserModel.fromJson(Map<String, dynamic> json) {
    name = json['name'];
    email = json['email'];
    uId = json['uId'];
    image = json['image'];
    isVerified = json['isVerified'];
  }
  Map<String, dynamic>? toMap() {
    return {
      'name': name,
      'email': email,
      'uId': uId,
      'image': image,
      'isVerified': isVerified,
    };
  }
}
