import 'package:flutter/material.dart';
import 'package:movie_proj/core/my_text.dart';
import 'package:movie_proj/feature/auth/widget/text_field_with_name.dart';

class SignupPassAndEmail extends StatelessWidget {
  const SignupPassAndEmail({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        TextFieldWithName(
          text: MyText.name,
          validator: (value) {
            if (value!.isEmpty) {
              return 'Name is required';
            }
            return null;
          },
          controller: TextEditingController(),
        ),
        TextFieldWithName(
          text: MyText.emailAddress,
          validator: (value) {
            if (value!.isEmpty) {
              return 'Email is required';
            }
            if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
              return 'Please enter a valid email';
            }
            return null;
          },
          controller: TextEditingController(),
        ),
        TextFieldWithName(
          text: MyText.password,
          validator: (value) {
            if (value!.isEmpty) {
              return 'Password is required';
            }
            if (value.length < 6) {
              return 'Password must be at least 6 characters';
            }
            return null;
          },
          controller: TextEditingController(),
          obscureText: true,
        ),
        TextFieldWithName(
          text: 'Confirm Password',
          validator: (value) {
            if (value!.isEmpty) {
              return 'Please confirm your password';
            }
            // Note: In a real app, you'd compare with the actual password field
            return null;
          },
          controller: TextEditingController(),
          obscureText: true,
        ),
      ],
    );
  }
}
