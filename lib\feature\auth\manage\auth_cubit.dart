import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:movie_proj/core/shared/shared_pref.dart';
import 'package:movie_proj/core/widget/message_snakbar.dart';
import 'package:movie_proj/feature/auth/manage/auth_state.dart';
import 'package:movie_proj/feature/auth/model/user_model.dart';
import 'package:connectivity_plus/connectivity_plus.dart';

class AuthCubit extends Cubit<AuthStates> {
  AuthCubit() : super(SignUpInitialStates()) {
    // Initialize without automatic login check to prevent null errors
    _initializeAuthCubit();
  }

  void _initializeAuthCubit() {
    try {
      // Initialize controllers safely
      emailController = TextEditingController();
      passwordController = TextEditingController();
      confirmPasswordController = TextEditingController();
      nameController = TextEditingController();

      // Initialize form keys
      formKeySignup = GlobalKey<FormState>();
      formKeyLogIN = GlobalKey<FormState>();
      formKeyEdit = GlobalKey<FormState>();

      if (kDebugMode) {
        debugPrint('AuthCubit initialized successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Error initializing AuthCubit: $e');
      }
      // Don't emit error state during initialization to prevent issues
    }
  }

  static AuthCubit get(context) => BlocProvider.of(context);
  bool isDoctor = true;
  bool isPasswordInVisible = true;
  IconData suffixIcon = Icons.visibility;
  late TextEditingController emailController;
  late TextEditingController passwordController;
  late TextEditingController confirmPasswordController;
  late TextEditingController nameController;

  late GlobalKey<FormState> formKeySignup;
  late GlobalKey<FormState> formKeyLogIN;
  late GlobalKey<FormState> formKeyEdit;

  changePasswordVisibility() {
    isPasswordInVisible = !isPasswordInVisible;
    isPasswordInVisible
        ? suffixIcon = Icons.visibility
        : suffixIcon = Icons.visibility_off;

    emit(ChangePasswordVisibilityStates());
  }

  changeBetweenDoctorOrPatient(bool isDoctoror) {
    isDoctor = isDoctoror;
    debugPrint('isDoctor: $isDoctor');
    emit(ChangeBetweenDoctorOrPatient());
  }

  bool isRetypePasswordInVisible = true;
  IconData retypePasswordSuffixIcon = Icons.visibility;

  changeReTypePasswordVisibility() {
    isRetypePasswordInVisible = !isRetypePasswordInVisible;
    isRetypePasswordInVisible
        ? retypePasswordSuffixIcon = Icons.visibility
        : retypePasswordSuffixIcon = Icons.visibility_off;

    emit(ChangeRetypePasswordVisibilityStates());
  }

  isUserNameValid(String userName) {
    if (userName.isEmpty) {
      return 'Username can\'t be Empty';
    } else if (userName.length > 30) {
      return 'Username can\'t be larger than 30 letter';
    } else if (userName.length < 2) {
      return 'Username can\'t be less than 2 letter';
    }
  }

  isPasswordValid(String password) {
    if (password.isEmpty) {
      return 'Password can\'t be Empty';
    } else if (password.length > 50) {
      return 'Password can\'t be larger than 50 digit';
    } else if (password.length < 6) {
      return 'Password can be at least 6 digit';
    }
  }

  matchPassword({required String value, required String password}) {
    if (value.isEmpty) {
      return 'Confirm password can\'t be empty';
    } else if (value != password) {
      return 'Passwords do not match';
    }
  }

  String? isEmailValid(String email) {
    if (RegExp(
            r"^[a-zA-Z0-9.a-zA-Z0-9.!#$%&'*+-/=?^_`{|}~]+@[a-zA-Z0-9]+\.[a-zA-Z]+")
        .hasMatch(email)) {
      return null;
    } else {
      return 'Enter a valid email';
    }
  }

  Future<void> userSignUp({
    required String email,
    required String password,
    required String name,
    String? image,
    required BuildContext context,
  }) async {
    emit(SignUpUserLoadingState());

    try {
      // 1. التحقق من الاتصال بالإنترنت أولاً
      final connectivityResult = await Connectivity().checkConnectivity();
      if (connectivityResult.contains(ConnectivityResult.none)) {
        emit(SignUpUserErrorState('No internet connection'));
        return;
      }

      // 2. إنشاء المستخدم في Firebase Auth
      UserCredential userCredential = await FirebaseAuth.instance
          .createUserWithEmailAndPassword(email: email, password: password);

      // 3. حفظ البيانات في الذاكرة المؤقتة
      await Future.wait([
        CacheHelper.saveData(key: 'name1', value: name),
      ]);

      if (kDebugMode) {
        debugPrint('User created successfully: ${userCredential.user?.uid}');
      }

      // 4. إنشاء سجل المستخدم في Firestore
      await userCreate(
        name,
        email,
        userCredential.user!.uid,
        image ?? '', // استخدام الصورة الافتراضية إذا لم يتم توفير صورة
      );

      // 5. إرسال رسالة التحقق بالبريد الإلكتروني (اختياري)
      await userCredential.user?.sendEmailVerification();

      // 6. إرسال حالة النجاح مع التحقق من وجود المعرف
      final userId = userCredential.user?.uid;
      if (userId != null && userId.isNotEmpty) {
        emit(SignUpUserSuccessState(userId));
      } else {
        emit(SignUpUserErrorState('Failed to get user ID after signup'));
      }
    } on FirebaseAuthException catch (e) {
      String errorMessage;

      switch (e.code) {
        case 'weak-password':
          errorMessage = 'Password is too weak (min 6 characters)';
          break;
        case 'email-already-in-use':
          errorMessage = 'Email is already registered';
          break;
        case 'invalid-email':
          errorMessage = 'Invalid email format';
          break;
        case 'operation-not-allowed':
          errorMessage = 'Email/password accounts are not enabled';
          break;
        case 'network-request-failed':
          errorMessage = 'Network error occurred';
          break;
        default:
          errorMessage = 'Signup failed: ${e.message}';
      }

      if (kDebugMode) {
        debugPrint('Firebase Auth Error: ${e.code} - ${e.message}');
      }

      // عرض الخطأ للمستخدم
      WidgetsBinding.instance.addPostFrameCallback((_) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(errorMessage)),
        );
      });

      emit(SignUpUserErrorState(errorMessage));
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Unexpected error: $e');
      }

      emit(SignUpUserErrorState('An unexpected error occurred'));

      WidgetsBinding.instance.addPostFrameCallback((_) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Registration failed')),
        );
      });
    }
  }

  Future<void> userCreate(
      String name, String email, String uId, String image) async {
    try {
      await FirebaseFirestore.instance.collection('users').doc(uId).set({
        'name': name,
        'email': email,
        'uId': uId,
        'image': image,
        'isVerified': false,
        'createdAt': FieldValue.serverTimestamp(),
      });

      if (kDebugMode) {
        debugPrint('User document created successfully for: $uId');
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Error creating user document: $e');
      }
      rethrow;
    }
  }

  UserModel? userModel;

  List<UserModel> allUsers = [];

  Future<void> getAllUsers() async {
    emit(GetAllUsersLoadingState());
    try {
      final snapshot =
          await FirebaseFirestore.instance.collection('users').get();

      allUsers =
          snapshot.docs.map((doc) => UserModel.fromJson(doc.data())).toList();

      emit(GetAllUsersSuccessState());
    } catch (e) {
      emit(GetAllUsersErrorState(e.toString()));
    }
  }

  Future<void> getUserData(uId) async {
    emit(GetUserLoadingState());
    try {
      final doc =
          await FirebaseFirestore.instance.collection('users').doc(uId).get();

      if (doc.exists && doc.data() != null) {
        userModel = UserModel.fromJson(doc.data()!);
        if (kDebugMode) {
          debugPrint('User data loaded: ${userModel!.name}');
          debugPrint('Email: ${userModel!.email}');
          debugPrint('UID: ${userModel!.uId}');
          debugPrint('Image: ${userModel!.image}');
        }
        emit(GetUserSuccessState());
      } else {
        emit(GetUserErrorState('User document not found'));
      }
    } catch (error) {
      emit(GetUserErrorState(error.toString()));
    }
  }

  Future<void> sendEmailVerification(BuildContext context) async {
    User? user = FirebaseAuth.instance.currentUser;
    if (user != null && !user.emailVerified) {
      await user.sendEmailVerification();
    }
  }

  Future<void> logout() async {
    try {
      // Sign out from Firebase
      await FirebaseAuth.instance.signOut();

      // Clear cached data
      await CacheHelper.removeData(key: 'uIdUser0');
      await CacheHelper.removeData(key: 'name1');

      // Clear user model
      userModel = null;

      // Clear form controllers
      emailController.clear();
      passwordController.clear();
      nameController.clear();
      confirmPasswordController.clear();

      emit(Unauthenticated());
    } catch (e) {
      emit(AuthErrorState(e.toString()));
    }
  }

  void listenToAuthStateChanges(user) async {
    try {
      if (user != null) {
        await user.reload();
        if (user.emailVerified) {
          try {
            await FirebaseFirestore.instance
                .collection('users')
                .doc(user.uid)
                .update({
              'isVerified': true,
            });

            if (kDebugMode) {
              print("User verification status updated successfully");
            }

            emit(EmailVerificationSuccessState());
          } catch (error) {
            if (kDebugMode) {
              print("Error updating user verification status: $error");
            }
          }
        }
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Error in listenToAuthStateChanges: $e');
      }
    }
  }

  Future<void> loginUsers(
      {required String email,
      required String password,
      required context}) async {
    emit(LogInUserLoadingState());
    try {
      UserCredential userCredential = await FirebaseAuth.instance
          .signInWithEmailAndPassword(email: email, password: password);

      // Reload user to get latest email verification status
      await userCredential.user!.reload();

      // Save user ID to cache for future login checks
      await CacheHelper.saveData(
          key: 'uIdUser0', value: userCredential.user!.uid);

      // Get user data from Firestore
      await FirebaseFirestore.instance
          .collection('users')
          .doc(userCredential.user!.uid)
          .get()
          .then((value) async {
        if (value.exists && value.data() != null) {
          // User document exists, proceed
          if (kDebugMode) {
            debugPrint('User document found for: ${userCredential.user!.uid}');
          }
        }
      });

      // إرسال حالة النجاح مع التحقق من وجود المعرف
      final userId = userCredential.user?.uid;
      if (userId != null && userId.isNotEmpty) {
        emit(LogInUserSuccessState(userId));
      } else {
        emit(LogInUserErrorState());
      }
    } on FirebaseAuthException catch (e) {
      if (e.code == 'user-not-found') {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          showAuthErrorSnackbar(
            context: context,
            code: e.code,
          );
        });
        emit(LogInDoctorErrorState());
      } else if (e.code == 'wrong-password') {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          showAuthErrorSnackbar(
            context: context,
            code: e.code,
          );
        });
        emit(LogInDoctorErrorState());
      }
    }
  }

  Future<void> checkLoginStatus() async {
    try {
      final uIdUser = CacheHelper.getString(key: 'uIdUser0');

      if (uIdUser != null && uIdUser.isNotEmpty) {
        await getUserData(uIdUser);
        emit(Authenticated());
      } else {
        emit(Unauthenticated());
      }
    } catch (e) {
      emit(AuthErrorState(e.toString()));
    }
  }

  //!----------update-----------------

  Future<void> updateProfileName({
    required String newName,
    required BuildContext context,
  }) async {
    final authCubit = BlocProvider.of<AuthCubit>(context);
    final currentUser = FirebaseAuth.instance.currentUser;

    if (currentUser == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('No authenticated user')),
      );
      return;
    }

    try {
      // تحديد المجموعة بناءً على نوع المستخدم

      // 1. تحديث في Firestore
      await FirebaseFirestore.instance
          .collection('users')
          .doc(currentUser.uid)
          .update({'name': newName});

      // 2. تحديث في Firebase Auth (display name)
      await currentUser.updateDisplayName(newName);

      // 3. تحديث في الذاكرة المؤقتة المحلية
      await CacheHelper.saveData(key: 'name1', value: newName);

      // 4. تحديث الحالة في Cubit
      if (authCubit.userModel != null) {
        authCubit.userModel!.name = newName;
      }
      // 5. إعادة جلب البيانات المحدثة

      await authCubit.getUserData(currentUser.uid);

      // 6. عرض رسالة نجاح
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم تحديث الاسم بنجاح'),
          backgroundColor: Colors.green,
        ),
      );

      emit(ProfileUpdatedSuccessState());
    } on FirebaseException catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('فشل في تحديث الاسم: ${e.message}'),
          backgroundColor: Colors.red,
        ),
      );
      emit(ProfileUpdateErrorState(e.message ?? 'Unknown error'));
    } catch (e) {
      if (kDebugMode) print('خطأ في تحديث الاسم: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('حدث خطأ ما'),
          backgroundColor: Colors.red,
        ),
      );
      emit(ProfileUpdateErrorState('Unknown error'));
    }
  }

  // في ملف AuthCubit
}
